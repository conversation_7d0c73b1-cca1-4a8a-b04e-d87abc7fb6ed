#!/usr/bin/env python3
"""
测试SPY数据缓存机制
验证在回测过程中SPY数据只获取一次
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.free_market_data import get_free_spy_momentum, clear_spy_cache, get_spy_cache_info
from src.tools.api import get_dynamic_market_context


def print_separator(title: str):
    """打印分隔线"""
    print("\n" + "=" * 60)
    print(f" {title} ")
    print("=" * 60)


def test_spy_cache_mechanism():
    """测试SPY缓存机制"""
    print_separator("SPY缓存机制测试")
    
    end_date = "2025-01-15"
    
    # 清理缓存
    print("🗑️  清理现有缓存...")
    clear_spy_cache()
    
    # 检查缓存状态
    cache_info = get_spy_cache_info()
    print(f"缓存状态: {cache_info}")
    
    print(f"\n📊 第一次获取SPY数据 (日期: {end_date})")
    print("=" * 40)
    spy_data_1 = get_free_spy_momentum(end_date, lookback_days=20)
    print(f"结果: {spy_data_1}")
    
    # 检查缓存状态
    cache_info = get_spy_cache_info()
    print(f"\n缓存状态: {cache_info}")
    
    print(f"\n📋 第二次获取SPY数据 (应该使用缓存)")
    print("=" * 40)
    spy_data_2 = get_free_spy_momentum(end_date, lookback_days=20)
    print(f"结果: {spy_data_2}")
    
    print(f"\n📋 第三次获取SPY数据 (应该使用缓存)")
    print("=" * 40)
    spy_data_3 = get_free_spy_momentum(end_date, lookback_days=20)
    print(f"结果: {spy_data_3}")
    
    # 验证数据一致性
    print(f"\n🔍 验证数据一致性:")
    print(f"第一次 == 第二次: {spy_data_1 == spy_data_2}")
    print(f"第二次 == 第三次: {spy_data_2 == spy_data_3}")
    
    return spy_data_1, spy_data_2, spy_data_3


def test_multiple_agents_simulation():
    """模拟多个代理调用SPY数据的情况"""
    print_separator("多代理SPY数据获取模拟")
    
    end_date = "2025-01-15"
    tickers = ["AAPL", "MSFT", "GOOGL"]
    
    # 清理缓存
    clear_spy_cache()
    
    print(f"模拟多个代理为不同股票获取市场环境数据...")
    print(f"测试股票: {tickers}")
    print(f"测试日期: {end_date}")
    
    results = {}
    
    for i, ticker in enumerate(tickers):
        print(f"\n--- 代理 {i+1}: 分析 {ticker} ---")
        
        try:
            # 模拟代理调用get_dynamic_market_context
            market_context = get_dynamic_market_context(ticker, end_date)
            
            # 提取SPY相关信息
            spy_info = {
                "relative_strength": market_context.get("relative_strength_vs_market"),
                "benchmark_available": market_context.get("market_benchmark_available"),
                "benchmark_source": market_context.get("market_benchmark_source")
            }
            
            results[ticker] = spy_info
            print(f"✅ {ticker} 市场环境分析完成")
            print(f"   相对强度: {spy_info['relative_strength']}")
            print(f"   基准可用: {spy_info['benchmark_available']}")
            print(f"   基准来源: {spy_info['benchmark_source']}")
            
        except Exception as e:
            print(f"❌ {ticker} 分析失败: {e}")
            results[ticker] = None
    
    # 检查缓存状态
    cache_info = get_spy_cache_info()
    print(f"\n📊 最终缓存状态: {cache_info}")
    
    return results


def test_cache_clearing():
    """测试缓存清理功能"""
    print_separator("缓存清理功能测试")
    
    end_date = "2025-01-15"
    
    # 获取数据并缓存
    print("📊 获取SPY数据...")
    spy_data = get_free_spy_momentum(end_date)
    
    cache_info = get_spy_cache_info()
    print(f"缓存状态: {cache_info}")
    
    # 清理缓存
    print("\n🗑️  清理缓存...")
    clear_spy_cache()
    
    cache_info_after = get_spy_cache_info()
    print(f"清理后缓存状态: {cache_info_after}")
    
    # 验证缓存已清理
    if cache_info_after["cache_size"] == 0:
        print("✅ 缓存清理成功")
    else:
        print("❌ 缓存清理失败")
    
    return cache_info, cache_info_after


def main():
    """主函数"""
    print("🔍 SPY数据缓存机制测试脚本")
    print("=" * 60)
    
    # 检查环境变量
    api_key = os.environ.get("FINANCIAL_DATASETS_API_KEY")
    if api_key:
        print(f"✅ 检测到 API Key: {api_key[:10]}...")
    else:
        print("⚠️  未检测到 FINANCIAL_DATASETS_API_KEY 环境变量")
        print("   某些功能可能受限")
    
    # 运行测试
    try:
        # 测试基本缓存机制
        spy_data_1, spy_data_2, spy_data_3 = test_spy_cache_mechanism()
        
        # 测试多代理模拟
        agent_results = test_multiple_agents_simulation()
        
        # 测试缓存清理
        cache_before, cache_after = test_cache_clearing()
        
        print_separator("测试总结")
        print("📝 关键发现:")
        
        # 验证缓存机制
        if spy_data_1 == spy_data_2 == spy_data_3:
            print("1. ✅ SPY数据缓存机制正常工作")
            print("2. 📋 多次调用返回相同数据，避免重复API请求")
        else:
            print("1. ❌ SPY数据缓存机制存在问题")
            print("2. 🔄 多次调用返回不同数据")
        
        # 验证多代理场景
        successful_agents = len([r for r in agent_results.values() if r is not None])
        print(f"3. 📊 {successful_agents}/{len(agent_results)} 个代理成功获取市场环境数据")
        
        # 验证缓存清理
        if cache_after["cache_size"] == 0:
            print("4. ✅ 缓存清理功能正常")
        else:
            print("4. ❌ 缓存清理功能异常")
        
        print("\n🎯 建议:")
        print("- 在回测系统中，每个交易日开始时调用 clear_spy_cache()")
        print("- 这样确保每日只获取一次SPY数据，所有代理共享")
        print("- 避免Yahoo Finance API超时和限制问题")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
