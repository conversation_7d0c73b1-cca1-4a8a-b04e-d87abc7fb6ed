# SPY数据缓存优化

## 问题描述

在回测过程中，每个代理都会独立调用`get_dynamic_market_context`函数来获取市场环境数据，而这个函数内部会调用`get_free_spy_momentum`来获取SPY数据。这导致了以下问题：

1. **重复API调用**：每个代理都尝试获取SPY数据，导致同一天内多次调用Yahoo Finance API
2. **API超时错误**：Yahoo Finance API有限制，多次调用容易导致超时
3. **性能问题**：重复的网络请求增加了回测时间
4. **资源浪费**：同样的数据被重复获取多次

## 解决方案

实现了SPY数据的日期缓存机制，确保在每个交易日内只获取一次SPY数据，所有代理共享这个数据。

### 核心改进

#### 1. 缓存机制 (`src/tools/free_market_data.py`)

```python
class FreeMarketDataProvider:
    def __init__(self):
        # 添加日期缓存
        self._daily_cache = {}
        self._cache_date = None
    
    def get_spy_momentum_with_fallback(self, end_date: str, lookback_days: int = 20) -> Dict:
        # 生成缓存键
        cache_key = f"{end_date}_{lookback_days}"
        
        # 检查是否已缓存当天数据
        if cache_key in self._daily_cache:
            print(f"📋 使用缓存的SPY数据 (日期: {end_date})")
            return self._daily_cache[cache_key]
        
        # 首次获取数据
        print(f"🔍 首次获取SPY数据 (日期: {end_date})")
        # ... API调用逻辑 ...
        
        # 缓存结果
        self._daily_cache[cache_key] = result
        return result
```

#### 2. 缓存管理功能

新增了以下函数：

- `clear_spy_cache()`: 清理SPY数据缓存
- `get_spy_cache_info()`: 获取缓存信息

#### 3. 线程安全机制

添加了线程锁确保并发安全：

```python
class FreeMarketDataProvider:
    def __init__(self):
        # 添加线程锁防止并发问题
        self._lock = threading.Lock()

    def get_spy_momentum_with_fallback(self, end_date: str, lookback_days: int = 20) -> Dict:
        # 使用线程锁确保线程安全
        with self._lock:
            # 双重检查锁定模式
            if cache_key in self._daily_cache:
                return self._daily_cache[cache_key]
            # ... API调用逻辑 ...
```

#### 4. 回测系统集成 (`src/backtester.py`)

在每个交易日结束时清理缓存：

```python
for i, current_date in enumerate(dates):
    # 在交易日开始时，不立即清理缓存
    # 让所有代理共享同一天的SPY数据

    # ... 代理执行逻辑 ...

    # 在交易日结束时清理SPY缓存，为下一个交易日做准备
    from src.tools.free_market_data import clear_spy_cache
    clear_spy_cache()
```

## 效果验证

通过多个测试脚本验证了缓存机制的有效性：

### 基础缓存测试 (`test_spy_cache.py`)

```
📝 关键发现:
1. ✅ SPY数据缓存机制正常工作
2. 📋 多次调用返回相同数据，避免重复API请求
3. 📊 3/3 个代理成功获取市场环境数据
4. ✅ 缓存清理功能正常
```

### 并发安全测试 (`test_concurrent_spy_cache.py`)

```
📝 关键发现:
1. ✅ SPY数据缓存机制在并发环境下正常工作
2. 📋 多个代理共享缓存数据，避免重复API调用
3. ✅ 线程安全机制正常，所有并发调用都成功
4. 🔒 线程锁确保数据一致性
```

**并发测试结果**：
- 8个并发代理调用，只有1次API请求
- 所有代理使用一致的基准数据源
- 线程安全机制防止竞态条件

### 性能改进

- **API调用次数**：从每个代理一次减少到每个交易日一次
- **网络请求**：大幅减少Yahoo Finance API调用
- **错误率**：显著降低API超时错误
- **回测速度**：提高了整体回测性能

## 使用方法

### 在回测中使用

缓存机制已自动集成到回测系统中，无需额外配置。

### 手动管理缓存

```python
from src.tools.free_market_data import get_free_spy_momentum, clear_spy_cache, get_spy_cache_info

# 获取SPY数据（自动缓存）
spy_data = get_free_spy_momentum("2025-01-15")

# 查看缓存状态
cache_info = get_spy_cache_info()
print(f"缓存状态: {cache_info}")

# 清理缓存
clear_spy_cache()
```

## 技术细节

### 缓存键格式

缓存键格式：`{end_date}_{lookback_days}`

例如：`2025-01-15_20`

### 缓存生命周期

- **创建**：首次调用`get_free_spy_momentum`时
- **使用**：同一交易日内的后续调用（使用线程锁确保安全）
- **清理**：每个交易日结束时自动清理，为下一个交易日做准备

### 错误处理

- 如果API调用失败，默认值也会被缓存
- 避免在同一交易日内重复尝试失败的API调用

## 监控和调试

### 日志输出

- `🔍 首次获取SPY数据`: 第一次API调用
- `📋 使用缓存的SPY数据`: 使用缓存数据
- `🗑️ SPY数据缓存已清理`: 缓存清理

### 缓存信息

```python
cache_info = get_spy_cache_info()
# 返回: {'cached_dates': ['2025-01-15_20'], 'cache_size': 1}
```

## 注意事项

1. **内存使用**：缓存会占用少量内存，但在每个交易日结束时会清理
2. **数据一致性**：同一交易日内所有代理使用相同的SPY数据
3. **API限制**：仍需遵守各API提供商的使用限制

## 未来改进

1. **持久化缓存**：考虑将缓存保存到文件，避免程序重启时重新获取
2. **多日缓存**：支持缓存多个交易日的数据
3. **缓存过期**：添加基于时间的缓存过期机制
4. **缓存统计**：添加缓存命中率等统计信息

## 总结

SPY数据缓存优化显著改善了回测系统的性能和稳定性：

- ✅ 解决了重复API调用问题
- ✅ 减少了Yahoo Finance API超时错误
- ✅ 提高了回测速度
- ✅ 保持了数据一致性
- ✅ 简化了代理间的数据共享
- ✅ 支持并发访问，线程安全
- ✅ 智能缓存管理，避免内存泄漏

这个优化确保了在每日回测时，SPY数据只需获取一次，所有代理都能高效、安全地共享这个数据，即使在多线程环境下也能正常工作。
