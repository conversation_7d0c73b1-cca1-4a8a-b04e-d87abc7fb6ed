import json
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate

from src.graph.state import AgentState, show_agent_reasoning
from pydantic import BaseModel, Field
from typing_extensions import Literal
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.agents.reflection_analyst import load_previous_reflection


class PortfolioDecision(BaseModel):
    action: Literal["buy", "sell", "short", "cover", "hold"]
    quantity: int = Field(description="Number of shares to trade")
    confidence: float = Field(description="Confidence in the decision, between 0.0 and 100.0")
    reasoning: str = Field(description="Reasoning for the decision")


class PortfolioManagerOutput(BaseModel):
    decisions: dict[str, PortfolioDecision] = Field(description="Dictionary of ticker to trading decisions")


##### Portfolio Management Agent #####
def portfolio_management_agent(state: AgentState):
    """Makes final trading decisions and generates orders for multiple tickers"""

    # Get the portfolio and analyst signals
    portfolio = state["data"]["portfolio"]
    analyst_signals = state["data"]["analyst_signals"]
    tickers = state["data"]["tickers"]
    end_date = state["data"]["end_date"]

    # 加载前一日的反思建议（如果存在）
    previous_reflections = load_previous_reflection(end_date, tickers)
    if previous_reflections:
        progress.update_status("portfolio_manager", None, "Loaded previous day reflections")
    else:
        progress.update_status("portfolio_manager", None, "No previous reflections found (first day or no data)")

    # Get position limits, current prices, and signals for every ticker
    position_limits = {}
    current_prices = {}
    max_shares = {}
    signals_by_ticker = {}
    for ticker in tickers:
        progress.update_status("portfolio_manager", ticker, "Processing analyst signals")

        # Get position limits and current prices for the ticker
        risk_data = analyst_signals.get("risk_management_agent", {}).get(ticker, {})
        position_limits[ticker] = risk_data.get("remaining_position_limit", 0)
        current_prices[ticker] = risk_data.get("current_price", 0)

        # Calculate maximum shares allowed based on position limit and price
        if current_prices[ticker] > 0:
            max_shares[ticker] = int(position_limits[ticker] / current_prices[ticker])
        else:
            max_shares[ticker] = 0

        # Get signals for the ticker
        ticker_signals = {}
        for agent, signals in analyst_signals.items():
            if agent != "risk_management_agent" and ticker in signals:
                # Extract basic signal and confidence
                signal_data = {
                    "signal": signals[ticker]["signal"],
                    "confidence": signals[ticker]["confidence"]
                }

                # Extract reasoning if available and convert to string format
                if "reasoning" in signals[ticker] and signals[ticker]["reasoning"] is not None:
                    reasoning = signals[ticker]["reasoning"]
                    # Handle both string and dict formats, convert to string
                    if isinstance(reasoning, dict):
                        signal_data["reasoning"] = str(reasoning)
                    else:
                        signal_data["reasoning"] = str(reasoning)
                else:
                    # No reasoning available for this agent
                    signal_data["reasoning"] = None

                ticker_signals[agent] = signal_data
        signals_by_ticker[ticker] = ticker_signals

    progress.update_status("portfolio_manager", None, "Generating trading decisions")

    # Generate the trading decision
    result = generate_trading_decision(
        tickers=tickers,
        signals_by_ticker=signals_by_ticker,
        current_prices=current_prices,
        max_shares=max_shares,
        portfolio=portfolio,
        previous_reflections=previous_reflections,
        model_name=state["metadata"]["model_name"],
        model_provider=state["metadata"]["model_provider"],
    )

    # Create the portfolio management message
    message = HumanMessage(
        content=json.dumps({ticker: decision.model_dump() for ticker, decision in result.decisions.items()}),
        name="portfolio_manager",
    )

    # Print the decision if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning({ticker: decision.model_dump() for ticker, decision in result.decisions.items()}, "Portfolio Manager")

    progress.update_status("portfolio_manager", None, "Done")

    return {
        "messages": state["messages"] + [message],
        "data": state["data"],
    }


def generate_trading_decision(
    tickers: list[str],
    signals_by_ticker: dict[str, dict],
    current_prices: dict[str, float],
    max_shares: dict[str, int],
    portfolio: dict[str, float],
    previous_reflections: dict[str, dict],
    model_name: str,
    model_provider: str,
) -> PortfolioManagerOutput:
    """Attempts to get a decision from the LLM with retry logic"""
    # Create the prompt template
    template = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """You are an ACTIVE portfolio manager making decisive trading decisions based on multiple analyst signals.

              CORE PHILOSOPHY: You are NOT a passive observer. Your job is to ACTIVELY trade when signals warrant action.
              Default to ACTION when you have clear signals, not to holding when uncertain.

              Trading Rules:
              - For long positions:
                * Only buy if you have available cash
                * Only sell if you currently hold long shares of that ticker
                * Sell quantity must be ≤ current long position shares
                * Buy quantity must be ≤ max_shares for that ticker

              - For short positions:
                * Only short if you have available margin (position value × margin requirement)
                * Only cover if you currently have short shares of that ticker
                * Cover quantity must be ≤ current short position shares
                * Short quantity must respect margin requirements

              - The max_shares values are pre-calculated to respect position limits
              - Consider both long and short opportunities based on signals
              - Maintain appropriate risk management with both long and short exposure

              Available Actions:
              - "buy": Open or add to long position
              - "sell": Close or reduce long position
              - "short": Open or add to short position
              - "cover": Close or reduce short position
              - "hold": No action (USE SPARINGLY - only when signals are truly mixed or weak)

              DECISION FRAMEWORK - FOLLOW THIS PROCESS:

              1. SIGNAL STRENGTH ASSESSMENT:
              - HIGH CONVICTION (≥75% confidence): Take FULL position (use max_shares or significant portion)
              - MEDIUM CONVICTION (50-74% confidence): Take MODERATE position (50-75% of max_shares)
              - LOW CONVICTION (<50% confidence): Take SMALL position (25-50% of max_shares) or hold
              - CONFLICTING SIGNALS: Analyze reasoning quality to break ties

              2. SIGNAL AGGREGATION RULES:
              - If 60%+ of agents agree on direction (bullish/bearish): STRONG signal - take action
              - If 40-60% agree: MODERATE signal - consider smaller position
              - If <40% agree: WEAK signal - hold or very small position
              - Weight high-confidence agents (>70%) more heavily than low-confidence agents

              3. REASONING QUALITY WEIGHTING:
              - Detailed, specific reasoning with data/metrics: HIGH weight
              - General market commentary without specifics: MEDIUM weight
              - Vague or generic reasoning: LOW weight
              - No reasoning (technical/valuation agents): Use confidence score only

              4. CONFLICT RESOLUTION:
              - When value investors (Buffett, Graham, Munger) disagree with growth investors (Fisher, Wood, Lynch):
                * Consider market conditions and stock characteristics
                * Value signals stronger for mature companies, growth signals stronger for innovative companies
              - When contrarians (Burry) disagree with momentum traders (Druckenmiller):
                * Consider recent price action and market sentiment
                * Contrarian signals stronger after major moves, momentum signals stronger in trending markets

              5. ACTION THRESHOLDS:
              - BUY: Net bullish signal with >55% agent agreement OR high-conviction bullish from 3+ agents
              - SELL: Net bearish signal with >55% agent agreement OR high-conviction bearish from 3+ agents
              - SHORT: Strong bearish consensus (>65% agreement) OR very high conviction bearish reasoning
              - HOLD: Only when signals are genuinely mixed (45-55% split) AND reasoning quality is similar

              Reflection Learning:
              - If previous day reflections are provided, carefully consider the insights and recommendations
              - Learn from past decision quality assessments to improve current decisions
              - Apply the specific recommendations from the reflection analyst when relevant

              Signal Analysis and Reasoning Integration:
              - Each analyst signal may include detailed reasoning explaining their investment thesis

              Value Investing Perspectives:
              - Warren Buffett agent provides value investing reasoning with margin of safety analysis
              - Ben Graham agent provides defensive value investing reasoning with net-net and asset-based analysis
              - Charlie Munger agent provides rational thinking reasoning with moat analysis and mental models

              Growth and Innovation Perspectives:
              - Phil Fisher agent provides growth investing reasoning with management quality emphasis
              - Cathie Wood agent provides disruptive innovation reasoning with exponential growth potential
              - Peter Lynch agent provides practical growth reasoning with PEG ratios and ten-bagger potential

              Activist and Contrarian Perspectives:
              - Bill Ackman agent provides activist investing reasoning with brand and operational improvement focus
              - Michael Burry agent provides contrarian reasoning with market inefficiency and deep value analysis

              Macro and Technical Perspectives:
              - Stanley Druckenmiller agent provides macro and momentum reasoning with risk-reward analysis
              - Aswath Damodaran agent provides academic valuation reasoning with DCF and relative valuation

              Fundamental and News Analysis:
              - Fundamentals agent provides quantitative fundamental analysis reasoning
              - Sentiment agent provides market sentiment reasoning based on insider trading and news
              - Factual News agent provides objective news analysis reasoning
              - Subjective News agent provides sentiment-driven news analysis reasoning

              Quantitative Signals (No Reasoning):
              - Technical analyst provides quantitative signals based on price action and market indicators
              - Valuation agent provides pure valuation metrics without detailed reasoning

              ACTIVE DECISION FRAMEWORK:
              - You are an ACTIVE trader, not a passive holder
              - When multiple agents provide clear signals in the same direction, ACT DECISIVELY
              - Use the signal strength assessment and aggregation rules defined above
              - Weight reasoning quality: detailed analysis > general commentary > no reasoning
              - Break ties using agent expertise: value experts for value stocks, growth experts for growth stocks
              - Consider market context: contrarian signals in overextended markets, momentum signals in trending markets
              - Default to ACTION when you have conviction, not to holding when uncertain

              Inputs:
              - signals_by_ticker: dictionary of ticker → signals (including reasoning where available)
              - max_shares: maximum shares allowed per ticker
              - portfolio_cash: current cash in portfolio
              - portfolio_positions: current positions (both long and short)
              - current_prices: current prices for each ticker
              - margin_requirement: current margin requirement for short positions (e.g., 0.5 means 50%)
              - total_margin_used: total margin currently in use
              - previous_reflections: insights and recommendations from previous day's decision analysis (if available)
              """,
            ),
            (
                "human",
                """MAKE ACTIVE TRADING DECISIONS for each ticker based on the comprehensive team analysis.

              REMEMBER: You are an ACTIVE portfolio manager. Your goal is to TRADE when signals warrant action.
              Do NOT default to "hold" unless signals are genuinely mixed or weak.

              Here are the signals by ticker (including detailed reasoning where available):
              {signals_by_ticker}

              DECISION PROCESS - FOLLOW THESE STEPS FOR EACH TICKER:

              1. COUNT THE SIGNALS:
              - How many agents are bullish vs bearish vs neutral?
              - What's the percentage agreement in each direction?

              2. ASSESS CONFIDENCE LEVELS:
              - Which agents have high confidence (>70%)?
              - Which agents have detailed, specific reasoning?

              3. APPLY DECISION RULES:
              - 60%+ agreement + high confidence = STRONG signal → Take FULL position
              - 40-60% agreement = MODERATE signal → Take PARTIAL position
              - <40% agreement = WEAK signal → Small position or hold
              - High-conviction minority (3+ agents >75% confidence) can override weak majority

              4. DETERMINE POSITION SIZE:
              - High conviction: Use 75-100% of max_shares
              - Medium conviction: Use 50-75% of max_shares
              - Low conviction: Use 25-50% of max_shares

              ANALYST REASONING ANALYSIS:

              Value Investing Reasoning:
              - Warren Buffett's reasoning focuses on intrinsic value, margin of safety, and long-term fundamentals
              - Ben Graham's reasoning emphasizes defensive value investing with asset protection and earnings stability
              - Charlie Munger's reasoning highlights rational thinking, moat analysis, and mental models

              Growth and Innovation Reasoning:
              - Phil Fisher's reasoning highlights growth quality, management efficiency, and innovation potential
              - Cathie Wood's reasoning focuses on disruptive innovation, exponential growth, and transformative technologies
              - Peter Lynch's reasoning emphasizes practical growth investing, PEG ratios, and understandable businesses

              Activist and Contrarian Reasoning:
              - Bill Ackman's reasoning emphasizes brand strength, activism potential, and operational improvements
              - Michael Burry's reasoning focuses on contrarian opportunities, market inefficiencies, and deep value

              Macro and Academic Reasoning:
              - Stanley Druckenmiller's reasoning covers macro trends, momentum, and asymmetric risk-reward
              - Aswath Damodaran's reasoning provides academic valuation analysis with DCF models and relative metrics

              Fundamental and News Analysis Reasoning:
              - Fundamentals agent reasoning covers quantitative financial metrics and ratios analysis
              - Sentiment agent reasoning analyzes market sentiment through insider trading and news patterns
              - Factual News agent reasoning provides objective analysis of company-specific news events
              - Subjective News agent reasoning interprets market sentiment and opinion-based news analysis

              Quantitative Signals (Limited Reasoning):
              - Technical analyst provides quantitative signals based on price action and market indicators
              - Valuation agent provides pure valuation metrics without detailed reasoning

              FINAL DECISION GUIDELINES:

              - BE DECISIVE: When you have clear signals (>55% agreement), take action
              - BE BOLD: When you have high conviction (multiple agents >75% confidence), use significant position sizes
              - BE SMART: Weight reasoning quality - detailed analysis beats generic commentary
              - BE ADAPTIVE: Consider market context when resolving conflicts between different investment styles
              - BE ACTIVE: Your job is to trade profitably, not to avoid all risk

              EXAMPLES OF WHEN TO ACT:
              ✅ 4+ agents bullish with >60% average confidence → BUY (significant position)
              ✅ 3+ agents bearish with detailed reasoning → SELL/SHORT
              ✅ Mixed signals but 2+ high-conviction (>80%) agents agree → Follow high-conviction minority
              ✅ Value agents bullish on undervalued stock → BUY (even if growth agents neutral)
              ✅ Growth agents bearish on overvalued growth stock → SELL (even if value agents neutral)

              EXAMPLES OF WHEN TO HOLD:
              ❌ Signals split 50/50 with similar confidence levels
              ❌ All agents have low confidence (<50%)
              ❌ Reasoning is vague across all agents

              Current Prices:
              {current_prices}

              Maximum Shares Allowed For Purchases:
              {max_shares}

              Portfolio Cash: {portfolio_cash}
              Current Positions: {portfolio_positions}
              Current Margin Requirement: {margin_requirement}
              Total Margin Used: {total_margin_used}

              Previous Day Reflections (learn from these insights):
              {previous_reflections}

              Output strictly in JSON with the following structure:
              {{
                "decisions": {{
                  "TICKER1": {{
                    "action": "buy/sell/short/cover/hold",
                    "quantity": integer,
                    "confidence": float between 0 and 100,
                    "reasoning": "string"
                  }},
                  "TICKER2": {{
                    ...
                  }},
                  ...
                }}
              }}
              """,
            ),
        ]
    )

    # Generate the prompt
    prompt = template.invoke(
        {
            "signals_by_ticker": json.dumps(signals_by_ticker, indent=2),
            "current_prices": json.dumps(current_prices, indent=2),
            "max_shares": json.dumps(max_shares, indent=2),
            "portfolio_cash": f"{portfolio.get('cash', 0):.2f}",
            "portfolio_positions": json.dumps(portfolio.get("positions", {}), indent=2),
            "margin_requirement": f"{portfolio.get('margin_requirement', 0):.2f}",
            "total_margin_used": f"{portfolio.get('margin_used', 0):.2f}",
            "previous_reflections": json.dumps(previous_reflections, indent=2, ensure_ascii=False) if previous_reflections else "无前一日反思数据（首日交易或数据不可用）",
        }
    )

    # Create default factory for PortfolioManagerOutput
    def create_default_portfolio_output():
        return PortfolioManagerOutput(decisions={ticker: PortfolioDecision(action="hold", quantity=0, confidence=0.0, reasoning="Error in portfolio management, defaulting to hold") for ticker in tickers})

    return call_llm(prompt=prompt, model_name=model_name, model_provider=model_provider, pydantic_model=PortfolioManagerOutput, agent_name="portfolio_manager", default_factory=create_default_portfolio_output)
