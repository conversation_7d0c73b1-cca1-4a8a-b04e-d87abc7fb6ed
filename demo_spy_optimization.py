#!/usr/bin/env python3
"""
SPY数据缓存优化演示脚本
展示优化前后的差异
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.free_market_data import get_free_spy_momentum, clear_spy_cache
from src.tools.api import get_dynamic_market_context


def simulate_old_behavior():
    """模拟优化前的行为：每个代理都独立获取SPY数据"""
    print("🔄 模拟优化前的行为（每个代理独立获取SPY数据）")
    print("=" * 60)
    
    agents = ["warren_buffett", "michael_burry", "cathie_wood", "peter_lynch"]
    tickers = ["AAPL", "MSFT"]
    end_date = "2025-01-15"
    
    start_time = time.time()
    
    for agent in agents:
        for ticker in tickers:
            print(f"\n📊 {agent} 分析 {ticker}...")
            
            # 清理缓存，模拟每个代理都重新获取
            clear_spy_cache()
            
            try:
                # 每个代理都调用get_dynamic_market_context
                market_context = get_dynamic_market_context(ticker, end_date)
                print(f"   ✅ 获取市场环境数据成功")
                print(f"   相对强度: {market_context.get('relative_strength_vs_market', 'N/A'):.2f}")
                
            except Exception as e:
                print(f"   ❌ 获取失败: {e}")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️  总耗时: {total_time:.2f} 秒")
    print(f"📊 总API调用次数: {len(agents) * len(tickers)} 次")
    
    return total_time, len(agents) * len(tickers)


def simulate_new_behavior():
    """模拟优化后的行为：所有代理共享缓存的SPY数据"""
    print("\n🚀 模拟优化后的行为（所有代理共享缓存的SPY数据）")
    print("=" * 60)
    
    agents = ["warren_buffett", "michael_burry", "cathie_wood", "peter_lynch"]
    tickers = ["AAPL", "MSFT"]
    end_date = "2025-01-15"
    
    # 清理缓存，模拟新交易日开始
    clear_spy_cache()
    
    start_time = time.time()
    api_calls = 0
    
    for agent in agents:
        for ticker in tickers:
            print(f"\n📊 {agent} 分析 {ticker}...")
            
            try:
                # 所有代理都调用get_dynamic_market_context，但SPY数据会被缓存
                market_context = get_dynamic_market_context(ticker, end_date)
                print(f"   ✅ 获取市场环境数据成功")
                print(f"   相对强度: {market_context.get('relative_strength_vs_market', 'N/A'):.2f}")
                
                # 只有第一次调用会触发API请求
                if agent == agents[0] and ticker == tickers[0]:
                    api_calls = 1
                
            except Exception as e:
                print(f"   ❌ 获取失败: {e}")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️  总耗时: {total_time:.2f} 秒")
    print(f"📊 实际API调用次数: {api_calls} 次")
    
    return total_time, api_calls


def main():
    """主函数"""
    print("🎯 SPY数据缓存优化演示")
    print("=" * 60)
    print("这个演示展示了优化前后的性能差异")
    print("优化前：每个代理都独立获取SPY数据")
    print("优化后：所有代理共享缓存的SPY数据")
    
    # 检查环境变量
    api_key = os.environ.get("FINANCIAL_DATASETS_API_KEY")
    if api_key:
        print(f"\n✅ 检测到 API Key: {api_key[:10]}...")
    else:
        print("\n⚠️  未检测到 FINANCIAL_DATASETS_API_KEY 环境变量")
        print("   某些功能可能受限")
    
    try:
        # 模拟优化前的行为
        old_time, old_calls = simulate_old_behavior()
        
        # 等待一下，避免API限制
        print("\n⏳ 等待3秒，避免API限制...")
        time.sleep(3)
        
        # 模拟优化后的行为
        new_time, new_calls = simulate_new_behavior()
        
        # 对比结果
        print("\n" + "=" * 60)
        print("📊 性能对比结果")
        print("=" * 60)
        
        print(f"优化前:")
        print(f"  ⏱️  耗时: {old_time:.2f} 秒")
        print(f"  📞 API调用: {old_calls} 次")
        
        print(f"\n优化后:")
        print(f"  ⏱️  耗时: {new_time:.2f} 秒")
        print(f"  📞 API调用: {new_calls} 次")
        
        # 计算改进
        time_improvement = ((old_time - new_time) / old_time) * 100 if old_time > 0 else 0
        call_reduction = ((old_calls - new_calls) / old_calls) * 100 if old_calls > 0 else 0
        
        print(f"\n🎯 改进效果:")
        print(f"  ⚡ 时间节省: {time_improvement:.1f}%")
        print(f"  📉 API调用减少: {call_reduction:.1f}%")
        
        print(f"\n✨ 主要优势:")
        print(f"  1. 减少了 {old_calls - new_calls} 次不必要的API调用")
        print(f"  2. 避免了Yahoo Finance API超时错误")
        print(f"  3. 提高了回测系统的稳定性")
        print(f"  4. 所有代理使用一致的SPY数据")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
