#!/usr/bin/env python3
"""
免费市场数据API集成
提供多个免费数据源来获取SPY和其他市场基准数据
"""

import requests
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd


class FreeMarketDataProvider:
    """
    免费市场数据提供商
    包含日期缓存机制，避免同一天重复获取SPY数据
    """

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        # 添加日期缓存 - 按日期缓存SPY数据
        self._daily_cache = {}
        self._cache_date = None
        # 添加线程锁防止并发问题
        self._lock = threading.Lock()
    
    def get_spy_data_yahoo(self, end_date: str, lookback_days: int = 20) -> Optional[Dict]:
        """
        使用Yahoo Finance免费API获取SPY数据
        """
        try:
            # 计算开始日期
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            start_dt = end_dt - timedelta(days=lookback_days + 10)
            
            # Yahoo Finance API (免费但有限制)
            start_timestamp = int(start_dt.timestamp())
            end_timestamp = int(end_dt.timestamp())
            
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/SPY"
            params = {
                'period1': start_timestamp,
                'period2': end_timestamp,
                'interval': '1d',
                'includePrePost': 'false'
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('chart', {}).get('result', [])
                
                if result:
                    timestamps = result[0].get('timestamp', [])
                    quotes = result[0].get('indicators', {}).get('quote', [{}])[0]
                    closes = quotes.get('close', [])
                    
                    if len(closes) >= 2:
                        # 计算动量
                        current_price = closes[-1]
                        past_price = closes[0] if len(closes) > lookback_days else closes[0]
                        momentum = (current_price - past_price) / past_price * 100
                        
                        # 计算波动率
                        returns = []
                        for i in range(1, len(closes)):
                            if closes[i-1] and closes[i]:
                                daily_return = (closes[i] - closes[i-1]) / closes[i-1]
                                returns.append(daily_return)
                        
                        import numpy as np
                        volatility = np.std(returns) * 100 if returns else 0
                        
                        return {
                            "momentum": momentum,
                            "volatility": volatility,
                            "trend": self._determine_trend(momentum),
                            "current_price": current_price,
                            "data_source": "yahoo_finance"
                        }
            
            return None
            
        except Exception as e:
            print(f"Yahoo Finance API error: {e}")
            return None
    
    def get_spy_data_alpha_vantage(self, end_date: str, lookback_days: int = 20) -> Optional[Dict]:
        """
        使用Alpha Vantage免费API获取SPY数据
        需要免费API密钥: https://www.alphavantage.co/support/#api-key
        """
        try:
            import os
            api_key = os.environ.get("ALPHA_VANTAGE_API_KEY")
            if not api_key:
                print("Alpha Vantage API key not found in environment variables")
                return None
            
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'TIME_SERIES_DAILY',
                'symbol': 'SPY',
                'apikey': api_key,
                'outputsize': 'compact'  # 最近100个交易日
            }
            
            response = self.session.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                time_series = data.get('Time Series (Daily)', {})
                
                if time_series:
                    # 获取日期排序的价格数据
                    dates = sorted(time_series.keys(), reverse=True)
                    closes = []
                    
                    for date in dates[:lookback_days + 5]:  # 多取几天以防周末
                        close_price = float(time_series[date]['4. close'])
                        closes.append(close_price)
                    
                    if len(closes) >= 2:
                        # 计算动量
                        current_price = closes[0]
                        past_price = closes[-1] if len(closes) > lookback_days else closes[-1]
                        momentum = (current_price - past_price) / past_price * 100
                        
                        # 计算波动率
                        returns = []
                        for i in range(1, len(closes)):
                            daily_return = (closes[i-1] - closes[i]) / closes[i]
                            returns.append(daily_return)
                        
                        import numpy as np
                        volatility = np.std(returns) * 100 if returns else 0
                        
                        return {
                            "momentum": momentum,
                            "volatility": volatility,
                            "trend": self._determine_trend(momentum),
                            "current_price": current_price,
                            "data_source": "alpha_vantage"
                        }
            
            return None
            
        except Exception as e:
            print(f"Alpha Vantage API error: {e}")
            return None
    
    def get_spy_data_polygon(self, end_date: str, lookback_days: int = 20) -> Optional[Dict]:
        """
        使用Polygon.io免费API获取SPY数据
        免费账户每月5次API调用: https://polygon.io/
        """
        try:
            import os
            api_key = os.environ.get("POLYGON_API_KEY")
            if not api_key:
                print("Polygon API key not found in environment variables")
                return None
            
            # 计算日期范围
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            start_dt = end_dt - timedelta(days=lookback_days + 10)
            
            url = f"https://api.polygon.io/v2/aggs/ticker/SPY/range/1/day/{start_dt.strftime('%Y-%m-%d')}/{end_dt.strftime('%Y-%m-%d')}"
            params = {'apikey': api_key}
            
            response = self.session.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                if results:
                    closes = [result['c'] for result in results]  # 'c' is close price
                    
                    if len(closes) >= 2:
                        # 计算动量
                        current_price = closes[-1]
                        past_price = closes[0] if len(closes) > lookback_days else closes[0]
                        momentum = (current_price - past_price) / past_price * 100
                        
                        # 计算波动率
                        returns = []
                        for i in range(1, len(closes)):
                            daily_return = (closes[i] - closes[i-1]) / closes[i-1]
                            returns.append(daily_return)
                        
                        import numpy as np
                        volatility = np.std(returns) * 100 if returns else 0
                        
                        return {
                            "momentum": momentum,
                            "volatility": volatility,
                            "trend": self._determine_trend(momentum),
                            "current_price": current_price,
                            "data_source": "polygon"
                        }
            
            return None
            
        except Exception as e:
            print(f"Polygon API error: {e}")
            return None
    
    def _determine_trend(self, momentum: float) -> str:
        """根据动量确定趋势"""
        if momentum > 5:
            return "strong_bullish"
        elif momentum > 1:
            return "bullish"
        elif momentum < -5:
            return "strong_bearish"
        elif momentum < -1:
            return "bearish"
        else:
            return "neutral"
    
    def get_spy_momentum_with_fallback(self, end_date: str, lookback_days: int = 20) -> Dict:
        """
        使用多个免费API获取SPY数据，带有回退机制
        包含日期缓存和线程锁，避免同一天重复获取和并发问题
        """
        # 生成缓存键
        cache_key = f"{end_date}_{lookback_days}"

        # 使用线程锁确保线程安全
        with self._lock:
            # 再次检查缓存（双重检查锁定模式）
            if cache_key in self._daily_cache:
                print(f"📋 使用缓存的SPY数据 (日期: {end_date})")
                return self._daily_cache[cache_key]

            print(f"🔍 首次获取SPY数据 (日期: {end_date})")

            providers = [
                ("Yahoo Finance", self.get_spy_data_yahoo),
                ("Alpha Vantage", self.get_spy_data_alpha_vantage),
                ("Polygon", self.get_spy_data_polygon),
            ]

            for provider_name, provider_func in providers:
                try:
                    print(f"尝试使用 {provider_name} 获取SPY数据...")
                    result = provider_func(end_date, lookback_days)

                    if result:
                        print(f"✅ 成功从 {provider_name} 获取SPY数据")
                        # 缓存结果
                        self._daily_cache[cache_key] = result
                        return result
                    else:
                        print(f"⚠️  {provider_name} 未返回有效数据")

                except Exception as e:
                    print(f"❌ {provider_name} 出错: {e}")
                    continue

                # 添加延迟以避免API限制
                time.sleep(1)

            # 如果所有API都失败，返回默认值并缓存
            print("⚠️  所有免费API都无法获取SPY数据，使用默认值")
            fallback_result = {
                "momentum": 0,
                "volatility": 0,
                "trend": "neutral",
                "current_price": None,
                "data_source": "fallback_default"
            }
            # 也缓存默认值，避免重复尝试
            self._daily_cache[cache_key] = fallback_result
            return fallback_result

    def clear_cache(self):
        """清理缓存"""
        self._daily_cache.clear()
        print("🗑️  SPY数据缓存已清理")

    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return {
            "cached_dates": list(self._daily_cache.keys()),
            "cache_size": len(self._daily_cache)
        }


# 全局实例
_free_market_provider = FreeMarketDataProvider()


def get_free_spy_momentum(end_date: str, lookback_days: int = 20) -> Dict:
    """
    获取免费SPY动量数据的主要接口
    包含自动缓存管理
    """
    return _free_market_provider.get_spy_momentum_with_fallback(end_date, lookback_days)


def clear_spy_cache():
    """
    清理SPY数据缓存
    建议在新的回测日期开始时调用
    """
    _free_market_provider.clear_cache()


def get_spy_cache_info() -> Dict:
    """
    获取SPY缓存信息
    """
    return _free_market_provider.get_cache_info()


def test_free_apis():
    """测试所有免费API"""
    print("🔍 测试免费市场数据API")
    print("=" * 50)
    
    end_date = "2025-01-15"
    provider = FreeMarketDataProvider()
    
    # 测试Yahoo Finance
    print("\n📊 测试Yahoo Finance API...")
    yahoo_result = provider.get_spy_data_yahoo(end_date)
    if yahoo_result:
        print(f"✅ Yahoo Finance成功: 动量={yahoo_result['momentum']:.2f}%, 波动率={yahoo_result['volatility']:.2f}%")
    else:
        print("❌ Yahoo Finance失败")
    
    # 测试Alpha Vantage
    print("\n📊 测试Alpha Vantage API...")
    av_result = provider.get_spy_data_alpha_vantage(end_date)
    if av_result:
        print(f"✅ Alpha Vantage成功: 动量={av_result['momentum']:.2f}%, 波动率={av_result['volatility']:.2f}%")
    else:
        print("❌ Alpha Vantage失败")
    
    # 测试Polygon
    print("\n📊 测试Polygon API...")
    polygon_result = provider.get_spy_data_polygon(end_date)
    if polygon_result:
        print(f"✅ Polygon成功: 动量={polygon_result['momentum']:.2f}%, 波动率={polygon_result['volatility']:.2f}%")
    else:
        print("❌ Polygon失败")
    
    # 测试回退机制
    print("\n🔄 测试回退机制...")
    fallback_result = get_free_spy_momentum(end_date)
    print(f"📊 最终结果: {fallback_result}")


if __name__ == "__main__":
    test_free_apis()
